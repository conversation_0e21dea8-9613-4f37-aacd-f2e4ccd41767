# 📸 智能拍照搜题系统

一个完整的智能拍照搜题解决方案，集成了人脸识别、双摄像头拍照、AI作业分析和数据库管理功能。

## 🌟 主要功能

### 1. 双模式支持
- **🏫 学校模式**：需要人脸识别验证身份，结果保存到学生信息数据库
- **🏠 家庭模式**：直接拍照搜题，结果保存到错误记录数据库

### 2. 人脸识别
- 基于InsightFace的高精度人脸识别
- 支持已知人员数据库管理
- 实时识别验证学生身份

### 3. 双摄像头系统
- 摄像头0：人脸识别专用
- 摄像头1：作业拍照专用
- 自动清理旧照片

### 4. AI作业分析
- 集成通义千问QVQ-Plus模型
- 自动识别错误题号
- 分析薄弱知识点
- 详细的解题思考过程

### 5. 数据库管理
- MySQL数据库存储
- 学校模式：student_info.student表
- 家庭模式：error.error_details表
- 自动创建数据库和表结构

### 6. MQTT控制
- 支持远程拍照控制
- 确认信号控制流程
- 与ESP32设备联动

## 🛠️ 系统架构

```
拍照搜题系统
├── photo_homework_main.py          # 主程序入口
├── photo_homework_handler.py       # 核心流程控制器
├── camera_handler.py               # 摄像头处理模块
├── database_handler.py             # 数据库处理模块
├── result_display.py               # 结果展示界面
├── face_rec.py                     # 人脸识别模块
├── upload.py                       # AI分析上传模块
├── mqtt_handler.py                 # MQTT通信模块
└── config.py                       # 配置文件
```

## 📋 环境要求

### Python版本
- Python 3.7+

### 依赖库
```bash
pip install -r requirements.txt
```

主要依赖：
- PyQt5 >= 5.15.0 (GUI界面)
- paho-mqtt >= 1.6.0 (MQTT通信)
- pymysql >= 1.0.0 (MySQL数据库)
- opencv-python >= 4.5.0 (摄像头控制)
- numpy >= 1.21.0 (数值计算)
- insightface >= 0.7.0 (人脸识别)
- openai >= 1.0.0 (AI模型调用)

### 硬件要求
- 双摄像头（USB或内置）
- 网络连接（用于AI分析和MQTT）
- MySQL数据库服务器

## ⚙️ 配置说明

### 1. 数据库配置
编辑 `config.py` 文件：

```python
DATABASE_CONFIG = {
    'host': 'poem.e5.luyouxia.net',
    'port': 28298,
    'user': 'your_username',      # 修改为实际用户名
    'password': 'your_password',  # 修改为实际密码
    'charset': 'utf8mb4'
}
```

### 2. MQTT配置
```python
MQTT_CONFIG = {
    'broker': '************',
    'port': 1883,
    'control_topic': 'esp32/s2/control',
    'notification_topics': ['nf', 'room', 'roomclose'],
    'sa_topic': 'sa'
}
```

### 3. AI模型配置
编辑 `upload.py` 文件中的API密钥：

```python
api_key = "your_dashscope_api_key"  # 替换为实际API密钥
```

### 4. 摄像头配置
在 `camera_handler.py` 中可以修改摄像头ID：

```python
def __init__(self, face_camera_id: int = 0, photo_camera_id: int = 1):
```

## 🗄️ 数据库表结构

### 学校模式表（student_info.student）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT AUTO_INCREMENT | 主键 |
| name | VARCHAR(100) | 学生姓名 |
| gender | VARCHAR(10) | 性别 |
| subject | TEXT | 薄弱知识点（JSON格式） |
| teacher | VARCHAR(100) | 教师姓名 |

### 家庭模式表（error.error_details）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT AUTO_INCREMENT | 主键 |
| time | DATETIME | 记录时间 |
| error | TEXT | 错误题号（JSON格式） |
| details | TEXT | 薄弱知识点（JSON格式） |

## 📁 文件夹结构

运行前请确保以下文件夹存在：

```
project/
├── known/              # 已知人脸图片目录
│   ├── student1.jpg    # 学生照片
│   ├── student2.jpg
│   └── ...
├── paper_photos/       # 作业照片目录（自动创建和清理）
├── face_photos/        # 人脸识别照片目录（自动创建）
└── ...
```

## 🚀 使用流程

### 1. 启动系统
```bash
python photo_homework_main.py
```

### 2. 选择模式

#### 学校模式流程：
1. 点击"🏫 学校模式"按钮
2. 系统启动人脸识别摄像头
3. 学生站在摄像头前进行人脸识别
4. 识别成功后，等待MQTT拍照确认信号
5. 收到确认信号后自动拍照
6. 等待上传确认信号
7. 收到确认信号后分析作业
8. 保存结果到学生数据库
9. 显示结果界面

#### 家庭模式流程：
1. 点击"🏠 家庭模式"按钮
2. 等待MQTT拍照确认信号
3. 收到确认信号后自动拍照
4. 等待上传确认信号
5. 收到确认信号后分析作业
6. 保存结果到错误记录数据库
7. 显示结果界面

### 3. MQTT控制信号

系统监听 `esp32/s2/control` 主题的以下信号：
- `6-0-1` (confirm)：确认/拍照信号
- `6-0-2` (back)：返回/取消信号

## 📊 结果展示

完成分析后，系统会显示包含以下内容的结果界面：

1. **概览信息**
   - 学生姓名（学校模式）
   - 使用模式
   - 处理时间

2. **错误分析**
   - 错误题号列表
   - 薄弱知识点

3. **解题分析**
   - AI的详细思考过程
   - 解题步骤和分析

## 🔧 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头是否被其他程序占用
   - 确认摄像头ID配置是否正确
   - 尝试更换摄像头

2. **人脸识别失败**
   - 确保 `known` 文件夹中有学生照片
   - 检查照片质量和光线条件
   - 重新构建人脸特征缓存

3. **数据库连接失败**
   - 检查数据库配置信息
   - 确认网络连接
   - 验证数据库用户权限

4. **AI分析失败**
   - 检查API密钥是否正确
   - 确认网络连接
   - 验证API配额

5. **MQTT连接问题**
   - 检查MQTT服务器地址和端口
   - 确认网络连接
   - 验证主题订阅

### 日志查看

系统会输出详细的日志信息，包括：
- 系统初始化状态
- 人脸识别结果
- 拍照和上传进度
- 数据库操作状态
- 错误信息

## 🔒 安全注意事项

1. **API密钥安全**
   - 不要将API密钥提交到代码仓库
   - 定期更换API密钥
   - 限制API使用权限

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

3. **人脸数据保护**
   - 合理使用人脸识别功能
   - 保护学生隐私
   - 遵守相关法律法规

## 📈 扩展功能

可以考虑的未来扩展：

1. **多学科支持**：针对不同学科的特殊分析
2. **批量处理**：同时处理多张作业图片
3. **语音交互**：语音控制和结果播报
4. **移动端支持**：开发手机应用
5. **统计分析**：学习进度和趋势分析

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志输出
2. 网络连接状态
3. 硬件设备状态
4. 配置文件设置

---

**注意**：首次运行前请确保完成所有配置，并测试各个组件的连接状态。 