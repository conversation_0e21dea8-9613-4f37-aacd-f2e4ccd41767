# 🎯 智能学习助手集成指南

## 📋 系统概述

拍照搜题功能已成功集成到现有的智能学习助手中，现在支持：

- **🏫 学校模式**：人脸识别 → 拍照 → AI分析 → 保存到学生数据库
- **🏠 家庭模式**：直接拍照 → AI分析 → 保存到错误记录数据库

## 🚀 快速启动指南

### 1. 环境配置

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 配置数据库
编辑 `config.py` 文件：

```python
DATABASE_CONFIG = {
    'host': 'poem.e5.luyouxia.net',
    'port': 28298,
    'user': 'your_username',      # 替换为真实用户名
    'password': 'your_password',  # 替换为真实密码
    'charset': 'utf8mb4'
}
```

#### 配置AI API
编辑 `upload.py` 文件：

```python
api_key = "your_dashscope_api_key"  # 替换为真实API密钥
```

### 2. 人脸识别配置（仅学校模式需要）

在 `known` 文件夹中添加学生照片：

```
known/
├── 张三.jpg
├── 李四.jpg
├── 王五.png
└── ...
```

### 3. 启动系统

```bash
python main.py
```

## 🔄 使用流程

### 学校模式流程

1. **选择环境**：启动后选择"学校"环境
2. **选择功能**：点击"作业批改"图标
3. **人脸识别**：系统提示"请面向摄像头进行身份验证"
4. **等待信号**：人脸识别成功后，等待MQTT拍照信号(`6-0-1`)
5. **拍照确认**：收到拍照信号后自动拍照
6. **等待上传**：等待第二个MQTT确认信号(`6-0-1`)进行AI分析
7. **查看结果**：系统显示错误题号、薄弱知识点和详细分析

### 家庭模式流程

1. **选择环境**：启动后选择"家庭"环境
2. **选择功能**：点击"作业批改"图标
3. **准备拍照**：系统提示"请准备作业，等待拍照信号"
4. **等待信号**：等待MQTT拍照信号(`6-0-1`)
5. **拍照确认**：收到拍照信号后自动拍照
6. **等待上传**：等待第二个MQTT确认信号(`6-0-1`)进行AI分析
7. **查看结果**：系统显示错误题号、薄弱知识点和详细分析

## 📱 MQTT控制信号

系统监听 `esp32/s2/control` 主题：

| 信号 | 命令 | 功能 |
|------|------|------|
| `6-0-1` | confirm | 确认/拍照信号 |
| `6-0-2` | back | 返回/取消信号 |

### 信号时序
1. **第一个 `6-0-1`**：触发拍照
2. **第二个 `6-0-1`**：开始AI分析和上传
3. **`6-0-2`**：取消当前流程

## 🗄️ 数据库表结构

### 学校模式数据表（student_info.student）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| name | VARCHAR(100) | 学生姓名（来自人脸识别） |
| gender | VARCHAR(10) | 性别 |
| subject | TEXT | 薄弱知识点（JSON数组） |
| teacher | VARCHAR(100) | 教师姓名 |

### 家庭模式数据表（error.error_details）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| time | DATETIME | 记录时间 |
| error | TEXT | 错误题号（JSON数组） |
| details | TEXT | 薄弱知识点（JSON数组） |

## 🎨 界面说明

### 主界面集成

- **环境选择**：学校/家庭模式选择
- **功能图标**：作业批改图标集成在功能列表中
- **状态通知**：右上角显示流程状态和提示
- **结果展示**：独立的结果显示窗口

### 状态提示

系统会在界面右上角显示实时状态：

- "请面向摄像头进行身份验证"（学校模式）
- "请准备作业，等待拍照信号"（家庭模式）
- "正在进行身份验证..."
- "拍照成功，等待上传确认信号..."
- "分析完成，正在保存结果..."
- "流程完成，正在显示分析结果..."

## 🔧 系统架构

```
智能学习助手
├── main.py                     # 主界面（已集成拍照搜题）
├── photo_homework_handler.py   # 拍照搜题流程控制
├── camera_handler.py           # 双摄像头管理
├── face_rec.py                 # 人脸识别系统
├── database_handler.py         # 数据库操作
├── upload.py                   # AI分析上传
├── result_display.py           # 结果展示界面
├── mqtt_handler.py             # MQTT通信（共享）
└── config.py                   # 配置管理
```

## 🔍 故障排除

### 常见问题

1. **人脸识别失败**
   - 确保 `known` 文件夹中有学生照片
   - 检查摄像头光线和角度
   - 查看日志确认InsightFace是否正确安装

2. **摄像头无法打开**
   - 检查摄像头权限
   - 确认摄像头ID配置（camera_handler.py中的0和1）
   - 关闭其他占用摄像头的程序

3. **数据库连接失败**
   - 检查 `config.py` 中的数据库配置
   - 确认网络连接和数据库服务状态
   - 验证用户名和密码

4. **AI分析失败**
   - 检查 `upload.py` 中的API密钥
   - 确认网络连接
   - 验证API服务状态

5. **MQTT信号无响应**
   - 检查MQTT服务器连接
   - 确认主题订阅是否正确
   - 验证ESP32设备状态

### 日志查看

启动系统后，控制台会显示详细日志：

```
INFO - 应用程序启动完成
INFO - 拍照搜题处理器初始化成功
INFO - 信号连接设置完成
INFO - 选择环境: 学校
INFO - 启动学校模式 - 人脸识别验证
INFO - 人脸识别成功: 张三
INFO - 收到MQTT指令: confirm
INFO - 拍照成功
...
```

## 📊 功能对比

| 功能特性 | 学校模式 | 家庭模式 |
|----------|----------|----------|
| 人脸识别 | ✅ 必需 | ❌ 跳过 |
| 拍照功能 | ✅ 支持 | ✅ 支持 |
| AI分析 | ✅ 支持 | ✅ 支持 |
| 数据存储 | student表 | error_details表 |
| 学生信息 | ✅ 记录 | ❌ 不记录 |
| 错误记录 | ✅ 支持 | ✅ 支持 |
| 时间记录 | ❌ 不记录 | ✅ 记录 |

## 🔄 升级说明

从独立的拍照搜题系统升级到集成版本：

1. **保持兼容**：原有的MQTT控制信号完全兼容
2. **功能增强**：添加了环境区分和用户界面集成
3. **数据库扩展**：支持两种不同的数据存储模式
4. **界面优化**：集成到主界面，用户体验更佳

## 📞 技术支持

如遇问题，请：

1. 查看控制台日志输出
2. 检查配置文件设置
3. 验证硬件设备连接
4. 确认网络服务状态

---

**🎉 现在您可以享受完整集成的智能学习助手拍照搜题功能了！** 