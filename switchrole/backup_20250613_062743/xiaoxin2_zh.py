#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dotenv import load_dotenv  
import io 
import azure.cognitiveservices.speech as speechsdk
from openai import OpenAI
import time
import datetime  
import threading  
import json, ast
import platform  # 添加平台检测
import pygame  
import requests, json
from io import BytesIO 
import tempfile 
import logging
import subprocess

# 添加阿里云CosyVoice相关导入
import dashscope
from dashscope.audio.tts_v2 import *

from xiaoxin2_skill import *
from xiaoxin2_wakefromnetwork import *
from audio_player import play_audio, play_audio_blocking

# 添加ALSA模块导入
from alsa_speech_recognizer import get_alsa_recognizer, recognize_speech_once_alsa
from alsa_cosyvoice_tts import get_alsa_tts, text_to_speech_alsa

# 导入线程池管理器
from thread_pool_manager import get_thread_manager, ThreadType, shutdown_thread_manager

# 导入新的线程管理器和语音活动检测器
from conversation_thread_manager import (
    get_conversation_manager, create_and_start_conversation, 
    ConversationState, ConversationThreadManager
)
from voice_activity_detector import detect_voice_activity, quick_voice_check, VoiceActivityResult

# 导入持续唤醒词检测器
from continuous_wakeword_detector import start_continuous_detection, stop_continuous_detection, get_continuous_detector

# 配置日志级别，隐藏httpx的INFO日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

# 检测操作系统
IS_WINDOWS = platform.system().lower() == "windows"
IS_LINUX = platform.system().lower() == "linux"

print(f"🔍 检测到操作系统: {platform.system()}")
print(f"🔧 Windows模式: {IS_WINDOWS}, Linux模式: {IS_LINUX}")

load_dotenv("xiaoxin.env")  

# 阿里云DashScope API配置
API_KEY = os.environ["DASHSCOPE_API_KEY"]
BASE_URL = os.environ["DASHSCOPE_BASE_URL"] 
MODEL_NAME = os.environ["DASHSCOPE_MODEL"]

# Set up OpenAI client
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

# 设置阿里云语音合成API Key
dashscope.api_key = API_KEY

# 设置Azure语音服务
speech_key = os.environ["Azure_speech_key"]
service_region = os.environ["Azure_speech_region"]

# Set up Azure Speech Service for Chinese (Simplified) recognition
speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)
speech_config.speech_recognition_language = "zh-CN"

# Create a speech recognizer
audio_config = speechsdk.audio.AudioConfig(use_default_microphone=True)
speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config, audio_config=audio_config)

# Azure Speech Service configuration
subscription_key = os.environ["Azure_speech_key"]
region = os.environ["Azure_speech_region"]

# Keyword recognition configuration
lang = "zh-CN"
keyword = os.environ["WakeupWord"]
model = speechsdk.KeywordRecognitionModel(os.environ["WakeupModelFile"])

# Initialize variables
messages = []
unknownCount = 0
done = False

# 程序状态标志
is_first_startup = True

# 添加全局变量控制后台监听
background_keyword_recognizer = None
in_conversation_mode = False  # 标记是否在对话模式中
background_listening_active = True  # 控制后台监听是否活跃

# 导入持续唤醒词检测器
from continuous_wakeword_detector import start_continuous_detection, stop_continuous_detection, get_continuous_detector

# 导入简化版GIF API客户端（修复Windows multiprocessing问题）
try:
    from gif_api_client_simple import (
        start_gif_service, stop_gif_service, gif_set_state, 
        gif_set_emotion, gif_set_emotion_from_text
    )
    GIF_INTEGRATION_AVAILABLE = True
    print("🎬 简化版GIF API客户端加载成功")
except ImportError as e:
    print(f"⚠️ 简化版GIF API客户端不可用: {e}")
    GIF_INTEGRATION_AVAILABLE = False

# 音频播放控制标志
tts_interrupt_flag = False  # TTS播放中断标志
music_paused = False  # 音乐暂停状态
music_playing = False  # 音乐播放状态
current_tts_thread = None  # 当前TTS播放线程

# 全局线程池管理器
thread_manager = get_thread_manager()
print(f"🎯 线程池管理器已初始化 (最大工作线程: 10)")

def convert_mp3_to_wav(mp3_file, output_wav=None):
    """
    将MP3文件转换为WAV格式
    
    Args:
        mp3_file: 输入的MP3文件路径
        output_wav: 输出的WAV文件路径，如果为None则自动生成
    
    Returns:
        str: 转换后的WAV文件路径，失败返回None
    """
    try:
        if output_wav is None:
            output_wav = mp3_file.replace('.mp3', '.wav')
        
        # 使用ffmpeg进行转换，使用更兼容的参数
        cmd = ['ffmpeg', '-i', mp3_file, '-ar', '48000', '-ac', '2', '-f', 'wav', output_wav, '-y']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ MP3转WAV成功: {output_wav}")
            return output_wav
        else:
            print(f"❌ MP3转WAV失败: {result.stderr}")
            return None
    except Exception as e:
        print(f"❌ MP3转WAV异常: {e}")
        return None

def play_mp3_audio(mp3_file, audio_type="tts"):
    """
    跨平台播放MP3音频文件（强力音频设备独占锁）
    
    Args:
        mp3_file: MP3文件路径
        audio_type: 音频类型 ("tts", "music", "system")
    
    Returns:
        bool: 播放成功返回True，失败或被中断返回False
    """
    global tts_interrupt_flag, music_paused, music_playing
    
    # 导入强力音频设备锁
    from audio_device_lock import get_audio_device_lock
    audio_lock = get_audio_device_lock()
    
    # 导入音频会话管理器
    from audio_session_manager import get_audio_session_manager
    session_manager = get_audio_session_manager()
    
    requester = f"播放_{audio_type}_{os.path.basename(mp3_file)}"
    
    try:
        # 检查文件是否存在
        if not os.path.exists(mp3_file):
            print(f"❌ 音频文件不存在: {mp3_file}")
            return False
        
        print(f"🎵 开始播放音频文件: {mp3_file} (类型: {audio_type}, 系统: {platform.system()})")
        
        # 获取音频设备独占锁（强制终止录音）
        if not audio_lock.acquire_for_playback(requester, timeout=10.0):
            print(f"❌ 无法获取音频设备播放锁")
            return False
        
        # 启动播放会话（会自动暂停录音）
        if not session_manager.start_playback(audio_type):
            print(f"❌ 无法启动音频播放会话")
            audio_lock.release(requester)
            return False
        
        # 根据音频类型处理不同的逻辑
        if audio_type == "music":
            music_playing = True
            music_paused = False
        elif audio_type == "tts":
            tts_interrupt_flag = False
        
        try:
            # 根据操作系统选择播放方式
            if IS_LINUX:
                # Linux系统：转换为WAV后使用audio_player播放
                result = _play_mp3_linux(mp3_file, audio_type)
            elif IS_WINDOWS:
                # Windows系统：使用pygame播放
                result = _play_mp3_windows(mp3_file, audio_type)
            else:
                print(f"❌ 不支持的操作系统: {platform.system()}")
                result = False
            
            return result
            
        finally:
            # 无论成功失败都要结束播放会话（会自动恢复录音）
            session_manager.finish_playback(audio_type)
            # 释放音频设备锁
            audio_lock.release(requester)
            
    except Exception as e:
        print(f"❌ 播放音频文件失败: {e}")
        # 清理状态
        if audio_type == "music":
            music_playing = False
            music_paused = False
        
        # 确保结束播放会话和释放设备锁
        session_manager.finish_playback(audio_type)
        audio_lock.release(requester)
        return False

def _play_mp3_linux(mp3_file, audio_type):
    """
    Linux系统播放MP3文件（转换为WAV后使用audio_player）
    """
    global tts_interrupt_flag, music_paused, music_playing
    
    try:
        # 将MP3转换为WAV
        wav_file = convert_mp3_to_wav(mp3_file)
        if not wav_file or not os.path.exists(wav_file):
            print(f"❌ MP3转WAV失败: {mp3_file}")
            return False
        
        print(f"🔄 Linux系统，使用audio_player播放WAV: {wav_file}")
        
        # 根据音频类型选择播放方式
        if audio_type == "tts":
            # TTS使用阻塞播放，支持中断检测
            return _play_wav_linux_with_interrupt(wav_file, audio_type)
        else:
            # 音乐和系统音频使用非阻塞播放
            success = play_audio(wav_file)
            
            # 清理临时WAV文件
            try:
                threading.Timer(5.0, lambda: os.remove(wav_file) if os.path.exists(wav_file) else None).start()
            except Exception as cleanup_error:
                print(f"警告: 无法安排WAV文件清理: {cleanup_error}")
            
            return success
            
    except Exception as e:
        print(f"❌ Linux播放MP3失败: {e}")
        return False

def _play_wav_linux_with_interrupt(wav_file, audio_type):
    """
    Linux系统播放WAV文件，支持TTS中断检测
    """
    global tts_interrupt_flag
    
    try:
        # 创建子线程进行阻塞播放
        play_thread = threading.Thread(target=lambda: play_audio_blocking(wav_file))
        play_thread.daemon = True
        play_thread.start()
        
        # 主线程检查中断标志
        while play_thread.is_alive():
            if audio_type == "tts" and tts_interrupt_flag:
                print(f"🛑 TTS播放被中断 (Linux): {wav_file}")
                # 停止所有音频播放
                from audio_player import stop_all_audio
                stop_all_audio()
                
                # 清理临时WAV文件
                try:
                    threading.Timer(1.0, lambda: os.remove(wav_file) if os.path.exists(wav_file) else None).start()
                except Exception as cleanup_error:
                    print(f"警告: 无法安排WAV文件清理: {cleanup_error}")
                
                return False
            
            time.sleep(0.1)
        
        # 播放完成，清理临时WAV文件
        try:
            threading.Timer(2.0, lambda: os.remove(wav_file) if os.path.exists(wav_file) else None).start()
        except Exception as cleanup_error:
            print(f"警告: 无法安排WAV文件清理: {cleanup_error}")
        
        print(f"✅ Linux音频播放完成: {wav_file}")
        return True
        
    except Exception as e:
        print(f"❌ Linux WAV播放失败: {e}")
        return False

def _play_mp3_windows(mp3_file, audio_type):
    """
    Windows系统播放MP3文件（使用pygame）
    """
    global tts_interrupt_flag, music_paused, music_playing
    
    try:
        # 初始化pygame mixer
        if not pygame.mixer.get_init():
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        # 停止当前播放的所有音频
        pygame.mixer.stop()
        
        # 确保完全释放之前的资源
        if pygame.mixer.music.get_busy():
            pygame.mixer.music.stop()
        
        # 等待mixer完全停止
        time.sleep(0.1)
        
        # 加载并播放音频文件
        pygame.mixer.music.load(mp3_file)
        pygame.mixer.music.play()
        
        # 等待播放完成，同时检查中断标志
        while pygame.mixer.music.get_busy():
            # 检查TTS中断标志
            if audio_type == "tts" and tts_interrupt_flag:
                print(f"🛑 TTS播放被中断 (Windows): {mp3_file}")
                pygame.mixer.music.stop()
                # 等待完全停止
                time.sleep(0.1)
                return False
            
            # 检查音乐暂停标志
            if audio_type == "music" and music_paused:
                print(f"⏸️ 音乐播放被暂停 (Windows): {mp3_file}")
                pygame.mixer.music.pause()
                # 等待继续播放或完全停止
                while music_paused and music_playing:
                    time.sleep(0.1)
                    if not music_playing:  # 如果被彻底停止
                        print(f"🛑 音乐播放被停止 (Windows): {mp3_file}")
                        pygame.mixer.music.stop()
                        # 等待完全停止
                        time.sleep(0.1)
                        return False
                # 如果需要继续播放
                if music_playing and not music_paused:
                    print(f"▶️ 音乐播放继续 (Windows): {mp3_file}")
                    pygame.mixer.music.unpause()
            
            time.sleep(0.1)
        
        # 播放完成后确保完全停止和清理状态
        pygame.mixer.music.stop()
        time.sleep(0.2)  # 等待完全释放
        
        if audio_type == "music":
            music_playing = False
            music_paused = False
        
        print(f"✅ Windows音频播放完成: {mp3_file}")
        return True
        
    except Exception as e:
        print(f"❌ Windows播放MP3失败: {e}")
        # 清理状态
        if audio_type == "music":
            music_playing = False
            music_paused = False
        
        # 确保停止播放
        try:
            pygame.mixer.music.stop()
            time.sleep(0.1)
        except:
            pass
        
        return False

# Define the speech-to-text function (ALSA version)
def speech_to_text():
    global unknownCount
    global lang
    print("🎤 请说话... (ALSA模式)")

    try:
        # 使用ALSA语音识别
        result = recognize_speech_once_alsa(timeout=5.0)
        if result:
            unknownCount = 0
            print(f"✅ ALSA识别成功: {result}")
            return result
        else:
            unknownCount += 1
            error = os.environ["sorry_"+lang] if f"sorry_{lang}" in os.environ else "抱歉，我没听清，请再说一遍。"
            text_to_speech_alsa(error)  # 使用ALSA TTS
            return error
    except Exception as e:
        print(f"❌ ALSA语音识别异常: {e}")
        unknownCount += 1
        error = "语音识别遇到问题，请重试。"
        text_to_speech_alsa(error)  # 使用ALSA TTS
        return error

# Define the text-to-speech function using ALSA CosyVoice (New Implementation)
def text_to_speech(text, is_welcome=False):
    """
    使用ALSA CosyVoice进行文本转语音（直接播放PCM流）
    
    Args:
        text: 要转换的文本
        is_welcome: 是否为欢迎语（用于区分优先级处理）
    """
    try:
        from audio_priority_manager import AudioPriority, request_audio_access, release_audio_access
        
        # 请求TTS播放权限
        requester_id = "欢迎语TTS" if is_welcome else "普通TTS"
        if not request_audio_access(AudioPriority.TTS, requester_id):
            print(f"⚠️ TTS播放权限被拒绝，当前有更高优先级音频")
            return False
        
        try:
            # 确保输入文本不为None且为有效字符串
            if text is None:
                print("⚠️ 输入文本为None，跳过语音合成")
                return False
            
            # 安全转换为字符串
            text_safe = str(text).strip()
            if not text_safe:
                print("⚠️ 输入文本为空，跳过语音合成")
                return False
            
            print(f"🎵 开始ALSA CosyVoice语音合成: {text_safe[:50]}...")
            
            # 计时语音合成
            synthesis_start = time.time()
            
            # 使用ALSA CosyVoice TTS（直接播放PCM流）
            success = text_to_speech_alsa(text_safe)
            
            synthesis_time = time.time() - synthesis_start
            print(f"⚡ ALSA语音合成完成 (耗时: {synthesis_time:.2f}秒)")
            
            return success
                
        except Exception as e:
            print(f"❌ ALSA CosyVoice语音合成异常: {e}")
            return False
        finally:
            # 无论成功失败都要释放权限
            release_audio_access(AudioPriority.TTS, requester_id)
            
    except Exception as ex:
        print(f"❌ ALSA语音合成错误: {ex}")
        return False

# Define the Azure OpenAI language generation function
def generate_text(prompt):
    global messages
    
    messages.append({"role": "user", "content": prompt})
    tools=getTools()
    cont = run_conversation(messages,tools)
    return cont["content"]

def getLLMResponse(messages,tools):
    i=20
    messages_ai = messages[-i:]
    while 'role' in messages_ai[0] and messages_ai[0]["role"] == 'tool':
        i+=1
        messages_ai = messages[-i:]
    sysmesg={"role": "system", "content": getSystemPrompt()}
    
    # 添加重试机制和超时控制
    max_retries = 2
    for attempt in range(max_retries):
        try:
            print(f"🚀 调用大模型API (尝试 {attempt + 1}/{max_retries})...")
            start_time = time.time()
            
            response = client.chat.completions.create(
                model=MODEL_NAME,
                messages=[sysmesg]+messages_ai,
                temperature=0.6,
                max_tokens=500,  # 减少token数量提高速度
                tools=tools,
                tool_choice="auto",
                stream=False,
                timeout=30  # 30秒超时
            )
            
            api_time = time.time() - start_time
            print(f"✅ 大模型API调用成功 (耗时: {api_time:.2f}秒)")
            return response.choices[0].message
            
        except Exception as e:
            print(f"❌ 大模型API调用失败 (尝试 {attempt + 1}): {e}")
            if attempt == max_retries - 1:
                # 最后一次尝试失败，返回错误回复
                return "抱歉，我现在遇到了一些技术问题，请稍后再试。"
            time.sleep(1)  # 重试前短暂等待

def run_conversation(messages,tools):
    # Step 1: send the conversation and available functions to the model
    response_message = getLLMResponse(messages,tools)
    
    # 将响应消息转换为字典格式
    if hasattr(response_message, 'model_dump'):
        response_dict = response_message.model_dump()
    else:
        response_dict = response_message
    
    # Step 2: check if the model wanted to call a function
    if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
        tool_calls = response_message.tool_calls
        # Step 3: call the function
        messages.append(response_dict)
        # Step 4: send the info for each function call and function response to the model
        for tool_call in tool_calls:
            print(f'⏳Call internal function...')
            function_name = tool_call.function.name
            print(f'⏳Call {function_name}...')
            function_to_call = globals()[function_name]
            function_args = json.loads(tool_call.function.arguments)
            
            print(f'⏳Call params: {function_args}')
            function_response = function_to_call(**function_args)
            print(f'⏳Call internal function done! ')
            print("执行结果：")
            print(function_response)
            print("===================================")
            messages.append(
                {
                    "tool_call_id": tool_call.id,
                    "role": "tool",
                    "name": function_name,
                    "content": function_response,
                }
            )
            
        response_message = run_conversation(messages,tools)
        return response_message
    else:
        return response_dict

# 唤醒词检测回调函数 - 兼容持续检测器
wake_event_flag = threading.Event()  # 全局唤醒事件标志
wake_event_data = {}  # 唤醒事件数据

def conversation_function(conversation_thread):
    """
    对话线程函数 - 处理单轮对话
    
    Args:
        conversation_thread: 对话线程对象（可以为None）
        
    Returns:
        str: 返回控制指令 ("continue", "terminate", "suspend")
    """
    global messages, unknownCount
    
    # 获取线程ID用于日志
    thread_id = conversation_thread.thread_id if conversation_thread else threading.current_thread().ident
    
    # 设置状态的辅助函数
    def set_state(state):
        if conversation_thread:
            conversation_thread.set_state(state)
    
    # 获取状态的辅助函数
    def get_state():
        if conversation_thread:
            return conversation_thread.get_state()
        return None
    
    try:
        # 设置状态为监听
        set_state(ConversationState.LISTENING if hasattr(globals().get('ConversationState', None), 'LISTENING') else None)
        
        # GIF API：设置聆听状态
        if GIF_INTEGRATION_AVAILABLE:
            gif_set_state("listening")
        
        print(f"🎤 [{thread_id}] 开始监听用户输入...")
        
        # 使用语音活动检测
        vad_result = detect_voice_activity(timeout=8.0)
        
        if not vad_result.has_speech:
            # 没有检测到有效语音
            print(f"🔇 [{thread_id}] 未检测到有效语音: {vad_result.reason}")
            
            # 检查是否被挂起（可能有新的唤醒词）
            current_state = get_state()
            if current_state and hasattr(globals().get('ConversationState', None), 'SUSPENDED') and current_state == ConversationState.SUSPENDED:
                print(f"⏸️ [{thread_id}] 线程被挂起，等待恢复或终止")
                return "suspend"
            
            # 检查原因，如果是检测超时且没有语音，则直接结束对话
            if "超时" in vad_result.reason or "未检测到清晰语音" in vad_result.reason:
                unknownCount += 1
                if unknownCount >= 1:  # 减少重试次数，第一次无语音就结束
                    print(f"🔚 [{thread_id}] 无语音输入，结束对话")
                    # 不播放再见语音，直接结束避免干扰
                    return "terminate"
                else:
                    print(f"⚠️ [{thread_id}] 未检测到语音，提示用户重说")
                    text_to_speech(os.environ["sorry_"+lang])
                    return "terminate"  # 也改为直接结束
            else:
                # 其他错误，直接结束
                print(f"🔚 [{thread_id}] 检测异常，结束对话")
                return "terminate"
        
        # ✨ 检测到有效语音 - 立即中断所有音频播放
        print(f"🛑 [{thread_id}] 检测到用户输入，立即中断当前音频播放")
        
        # 强制停止所有音频播放
        global tts_interrupt_flag
        tts_interrupt_flag = True
        
        try:
            # 停止pygame音频
            if pygame.mixer.get_init():
                pygame.mixer.music.stop()
                pygame.mixer.stop()
        except:
            pass
        
        # 强制停止所有音频
        from audio_priority_manager import force_stop_all_audio
        from audio_player import stop_all_audio
        force_stop_all_audio(except_priority=None)
        stop_all_audio()
        
        # 等待音频完全停止
        time.sleep(0.1)
        
        user_input = vad_result.text
        unknownCount = 0  # 重置计数
        
        print(f"🗣️ [{thread_id}] 用户说: {user_input}")
        
        # 设置状态为处理中
        set_state(ConversationState.PROCESSING)
        
        # GIF API：设置AI思考状态
        if GIF_INTEGRATION_AVAILABLE:
            gif_set_state("thinking")
        
        # 检查退出命令（优先级最高）
        if any(keyword in user_input.lower() for keyword in ["退出语音", "退出", "再见", "结束对话", "结束语音"]):
            print(f"👋 [{thread_id}] 用户要求退出")
            text_to_speech("好的，再见！")
            return "terminate"
        
        # 检查其他音乐控制命令
        if handle_music_control_command(user_input):
            print(f"🎵 [{thread_id}] 处理音乐控制命令，结束对话避免干扰")
            return "terminate"  # 音乐控制后也终止对话线程
        
        # 正常AI对话处理 - 使用流式生成和语音合成
        print(f"🤖 [{thread_id}] 处理AI对话...")
        
        # 设置状态为AI回复中
        set_state(ConversationState.SPEAKING)
        
        # GIF API：设置AI回复状态
        if GIF_INTEGRATION_AVAILABLE:
            gif_set_emotion("thinking")  # 先设置为思考状态
        
        # 使用流式AI对话（边生成边语音合成）
        print(f"🚀 [{thread_id}] 开始流式AI对话...")
        total_start_time = time.time()
        
        response = None  # 初始化response变量
        
        try:
            # 导入流式语音合成模块
            from streaming_tts_player import streaming_ai_conversation_with_full_response
            
            # 准备对话参数（先备份原始messages，避免重复添加）
            original_messages = messages.copy()
            original_messages.append({"role": "user", "content": user_input})
            tools = getTools()
            
            # 智能检测是否为纯聊天场景
            chat_keywords = ["讲", "说", "聊", "故事", "笑话", "介绍", "谈", "告诉", "分享"]
            tool_keywords = ["播放", "音乐", "时间", "提醒", "解题", "计算", "退出", "切换", "设置"]
            
            is_chat_request = any(keyword in user_input for keyword in chat_keywords)
            is_tool_request = any(keyword in user_input for keyword in tool_keywords)
            
            # 检查是否需要工具调用 - 优先考虑聊天场景
            if tools and len(tools) > 0 and not is_chat_request and is_tool_request:
                print(f"🔧 [{thread_id}] 检测到工具调用需求，使用传统对话确保工具调用正常")
                # 使用传统方式处理工具调用
                response = generate_text(user_input)
                text_to_speech(response)
                total_time = time.time() - total_start_time
                print(f"✅ [{thread_id}] 传统语音播放完成 (总耗时: {total_time:.2f}秒)")
                return "terminate"  # 改为terminate，一次唤醒只对话一次
            elif is_chat_request or not tools or len(tools) == 0:
                print(f"💬 [{thread_id}] 检测到纯聊天场景，使用流式模式获得更快响应")
            else:
                print(f"🤔 [{thread_id}] 检测到 {len(tools)} 个工具，但优先尝试流式对话")
            
            # 执行流式对话（优先用于聊天场景，工具调用时作为降级处理）
            success, full_response = streaming_ai_conversation_with_full_response(
                client, original_messages, MODEL_NAME, None if is_chat_request else tools
            )
            
            total_time = time.time() - total_start_time
            
            if success and full_response:
                print(f"✅ [{thread_id}] 流式AI对话完成 (总耗时: {total_time:.2f}秒)")
                print(f"🤖 [{thread_id}] 完整回复: {full_response[:100]}...")
                
                # 更新全局messages
                messages.append({"role": "user", "content": user_input})
                messages.append({"role": "assistant", "content": full_response})
                response = full_response  # 设置response用于后续逻辑
                
                # 设置合适的GIF情感状态
                if GIF_INTEGRATION_AVAILABLE:
                    gif_thread = threading.Thread(
                        target=lambda: gif_set_emotion_from_text(full_response), 
                        daemon=True
                    )
                    gif_thread.start()
                
            else:
                print(f"❌ [{thread_id}] 流式AI对话失败，使用传统方式")
                # 降级到传统方式
                response = generate_text(user_input)
                text_to_speech(response)
                total_time = time.time() - total_start_time
                print(f"✅ [{thread_id}] 传统语音播放完成 (总耗时: {total_time:.2f}秒)")
                
        except ImportError:
            print(f"⚠️ [{thread_id}] 流式模块不可用，使用传统方式")
            # 降级到传统方式
            response = generate_text(user_input)
            text_to_speech(response)
            total_time = time.time() - total_start_time
            print(f"✅ [{thread_id}] 传统语音播放完成 (总耗时: {total_time:.2f}秒)")
        except Exception as e:
            print(f"❌ [{thread_id}] 流式对话异常: {e}")
            # 降级到传统方式
            response = generate_text(user_input)
            text_to_speech(response)
            total_time = time.time() - total_start_time
            print(f"✅ [{thread_id}] 传统语音播放完成 (总耗时: {total_time:.2f}秒)")
        
        # 检查提醒
        checkReminders(text_to_speech)
        
        # GIF API：对话轮次结束，延迟返回待机状态  
        if GIF_INTEGRATION_AVAILABLE:
            # 延迟一段时间后返回待机状态
            def delayed_idle():
                time.sleep(3)  # 等待3秒
                gif_set_state("idle")
            threading.Timer(0, delayed_idle).start()
        
        print(f"✅ [{thread_id}] 对话轮次完成")
        print(f"🔚 [{thread_id}] 单次对话结束，终止线程避免干扰音乐播放")
        return "terminate"  # 改为terminate，一次唤醒只对话一次
        
    except Exception as e:
        print(f"❌ [{thread_id}] 对话函数异常: {e}")
        return "continue"

def continuous_wake_callback(wake_event):
    """持续唤醒词检测回调函数"""
    global wake_event_flag, wake_event_data, in_conversation_mode, tts_interrupt_flag, music_paused, music_playing
    
    print(f"🎯 持续检测器触发唤醒: {wake_event['text']}")
    
    # 检查是否已经在处理唤醒事件
    if in_conversation_mode or wake_event_flag.is_set():
        print("⚠️ 已有对话正在进行，忽略此次唤醒")
        return
    
    # 保存唤醒事件数据
    wake_event_data.update(wake_event)
    
    # 获取对话管理器
    conv_manager = get_conversation_manager()
    
    # 检查当前是否有活跃对话，立即终止
    if conv_manager.has_active_conversation():
        print("🛑 检测到活跃对话，立即终止当前对话线程以响应新唤醒")
        conv_manager.terminate_active_conversation()
        # 等待线程完全终止
        time.sleep(0.5)
    
    # 终止所有等待中的对话线程
    with thread_manager.status_lock:
        terminated_count = 0
        for thread_info in list(thread_manager.active_threads.values()):
            if (thread_info.thread_type == ThreadType.CONVERSATION_MANAGER and 
                "等待" in thread_info.status):
                try:
                    thread_info.future.cancel()
                    terminated_count += 1
                except:
                    pass
        if terminated_count > 0:
            print(f"🛑 已终止 {terminated_count} 个等待中的对话线程")
    
    # 执行唤醒处理逻辑
    handle_wake_event()
    
    # 使用线程池创建对话线程
    def conversation_wrapper():
        """对话处理包装函数"""
        try:
            # 使用简单的线程ID作为标识
            thread_id = threading.current_thread().ident
            print(f"🆕 对话线程启动 (ID: {thread_id})")
            
            # 直接调用对话函数，传入None作为conversation_thread参数
            result = conversation_function(None)
            print(f"🔚 对话线程完成，结果: {result}")
            return result
        except Exception as e:
            print(f"❌ 对话处理异常: {e}")
            return "error"
        finally:
            # 确保对话结束后重置状态
            global in_conversation_mode, tts_interrupt_flag
            in_conversation_mode = False
            tts_interrupt_flag = False
    
    conversation_future = thread_manager.submit_task(
        "语音对话处理",
        ThreadType.CONVERSATION_MANAGER,
        conversation_wrapper
    )
    print(f"🆕 创建新对话线程 (线程池管理)")
    
    # 设置唤醒事件标志
    wake_event_flag.set()

def handle_wake_event():
    """处理唤醒事件的核心逻辑（优雅音频中断版）"""
    global in_conversation_mode, tts_interrupt_flag, music_paused, music_playing, done
    
    print("🔊 执行唤醒处理流程...")
    
    # GIF API：设置聆听状态（唤醒后立即进入聆听）
    if GIF_INTEGRATION_AVAILABLE:
        gif_set_state("listening")
    
    # 🎯 优雅的音频中断策略（避免Broken pipe）
    print("🎵 优雅中断音频播放...")
    
    # 1. 设置全局中断标志（让流式播放自然结束）
    tts_interrupt_flag = True
    
    # 2. 通知ALSA TTS停止流式播放
    try:
        from alsa_cosyvoice_tts import get_alsa_tts
        alsa_tts = get_alsa_tts()
        if alsa_tts:
            alsa_tts.interrupt_playback()
    except Exception as e:
        print(f"⚠️ ALSA TTS中断通知失败: {e}")
    
    # 3. 通知流式播放器中断
    try:
        from streaming_tts_player import get_streaming_player
        player = get_streaming_player()
        if player:
            player.interrupt()
    except Exception as e:
        print(f"⚠️ 流式播放器中断通知失败: {e}")
    
    # 4. 停止pygame音频（非强制）
    try:
        if pygame.mixer.get_init():
            pygame.mixer.music.stop()
            pygame.mixer.stop()
    except:
        pass
    
    # 5. 更新播放状态
    if music_playing and not music_paused:
        print("⏸️ 暂停音乐播放")
        music_paused = True
    
    # 6. 标记进入对话模式
    in_conversation_mode = True
    
    # 7. 等待音频自然结束（给aplay进程时间正常关闭）
    print("⏳ 等待音频播放自然结束...")
    max_wait = 3.0  # 最多等待3秒
    wait_step = 0.1
    waited = 0
    
    while waited < max_wait:
        # 检查ALSA播放是否结束
        try:
            from alsa_cosyvoice_tts import get_alsa_tts
            alsa_tts = get_alsa_tts()
            if not alsa_tts or not alsa_tts.play_process:
                break  # aplay进程已结束
        except:
            break
        
        time.sleep(wait_step)
        waited += wait_step
    
    if waited >= max_wait:
        print("⚠️ 音频播放超时，执行强制停止")
        # 只有在超时的情况下才强制停止
        try:
            from audio_device_lock import get_audio_device_lock
            audio_lock = get_audio_device_lock()
            audio_lock.force_release("唤醒超时处理")
        except Exception as e:
            print(f"⚠️ 强制停止失败: {e}")
    else:
        print("✅ 音频播放已优雅结束")
    
    print("🔊 播放唤醒提示音")
    
    # 8. 播放唤醒提示音
    from audio_priority_manager import AudioPriority, request_audio_access, release_audio_access
    
    if request_audio_access(AudioPriority.TTS, "唤醒提示音"):
        try:
            if os.path.exists("wake.mp3"):
                print("🎵 播放唤醒提示音: wake.mp3")
                play_mp3_audio("wake.mp3", "system")
                print("✅ 唤醒提示音播放完成")
                
                # 等待声音完全消散
                print("⏳ 等待声音消散...")
                time.sleep(1.5)
                print("🎤 现在可以安全录音")
            else:
                print("⚠️ wake.mp3文件不存在，跳过播放")
        finally:
            release_audio_access(AudioPriority.TTS, "唤醒提示音")
    else:
        print("⚠️ 无法获取音频权限播放唤醒提示音")
        time.sleep(0.5)
    
    print("🎧 唤醒处理完成，等待对话线程开始监听")
    
    # 设置done标志以触发主循环进入交互模式
    done = True

# 传统唤醒词回调函数已被持续检测器替代，删除以减少代码复杂度

stop_do_reminder_loop=False
def do_reminder_loop():  
    """提醒检查循环 - 运行在线程池中"""
    print("🔔 启动提醒检查线程...")
    try:
        while not thread_manager.shutdown_event.is_set() and not stop_do_reminder_loop:
            # 更新线程活动时间
            current_thread = threading.current_thread()
            thread_id = current_thread.ident
            if thread_id in thread_manager.active_threads:
                thread_manager.active_threads[thread_id].last_activity = datetime.datetime.now()
            
            checkReminders(text_to_speech)
            time.sleep(3)
        print("🔔 提醒检查线程正常结束")
    except Exception as e:
        print(f"❌ 提醒检查线程异常: {e}")
    finally:
        print("🔔 提醒检查线程已停止")

def start_background_keyword_listening():
    """启动后台持续唤醒词监听 - 使用线程池管理"""
    global background_listening_active
    
    def wake_detection_task():
        """唤醒词检测任务 - 运行在线程池中"""
        try:
            print("🚀 启动持续唤醒词检测任务...")
            
            # 更新线程活动状态
            current_thread = threading.current_thread()
            thread_id = current_thread.ident
            
            detector = start_continuous_detection(continuous_wake_callback)
            
            if detector:
                print("✅ 持续唤醒词检测器启动成功")
                print("🎤 现在可以随时说'你好广和通'来唤醒助手，即使在对话过程中也有效")
                
                # 保持检测器运行，定期更新活动状态
                while not thread_manager.shutdown_event.is_set() and background_listening_active:
                    # 更新线程活动时间
                    if thread_id in thread_manager.active_threads:
                        thread_manager.active_threads[thread_id].last_activity = datetime.datetime.now()
                    
                    # 检查检测器状态
                    if detector and hasattr(detector, 'is_running') and not detector.is_running():
                        print("⚠️ 检测器停止运行，尝试重启...")
                        detector = start_continuous_detection(continuous_wake_callback)
                    
                    time.sleep(5)  # 每5秒检查一次状态
                
                print("🛑 持续唤醒词检测任务正常结束")
            else:
                print("❌ 持续唤醒词检测器启动失败")
                
        except Exception as e:
            print(f"❌ 唤醒词检测任务异常: {e}")
        finally:
            print("🔇 唤醒词检测任务已停止")
    
    print("🚀 启动增强版后台持续唤醒词监听...")
    background_listening_active = True
    
    # 使用线程池启动持久任务
    try:
        thread_manager.start_persistent_thread(
            "持续唤醒词检测",
            ThreadType.WAKE_WORD_DETECTOR,
            wake_detection_task,
            auto_restart=True
        )
        return True
    except Exception as e:
        print(f"❌ 启动唤醒词检测线程失败: {e}")
        background_listening_active = False
        return False

def stop_background_keyword_listening():
    """停止后台唤醒词监听"""
    global background_listening_active
    
    background_listening_active = False
    
    try:
        stop_continuous_detection()
        print("🔇 持续唤醒词检测器已停止")
    except Exception as e:
        print(f"⚠️ 停止持续唤醒词检测器时出错: {e}")

def handle_music_control_command(user_input):
    """
    处理音乐控制命令
    
    Args:
        user_input: 用户输入的文本
    
    Returns:
        bool: 如果是音乐控制命令返回True，否则返回False
    """
    global music_paused, music_playing
    
    user_input_lower = user_input.lower().strip()
    
    # 继续播放命令
    if any(keyword in user_input_lower for keyword in ["继续播放", "继续", "恢复播放", "继续音乐", "恢复音乐"]):
        if music_playing and music_paused:
            print("▶️ 用户命令：继续播放音乐")
            music_paused = False
            text_to_speech("好的，继续播放音乐")
            return True
        elif not music_playing:
            print("⚠️ 当前没有暂停的音乐")
            text_to_speech("当前没有暂停的音乐哦")
            return True
        else:
            print("⚠️ 音乐已经在播放中")
            text_to_speech("音乐已经在播放中了")
            return True
    
    # 暂停播放命令
    elif any(keyword in user_input_lower for keyword in ["暂停播放", "暂停", "暂停音乐", "停止播放", "停止音乐"]):
        if music_playing:
            print("⏸️ 用户命令：暂停/停止音乐播放")
            music_playing = False
            music_paused = False
            text_to_speech("好的，已停止音乐播放")
            return True
        else:
            print("⚠️ 当前没有音乐在播放")
            text_to_speech("当前没有音乐在播放哦")
            return True
    

    
    return False

# 获取当前时间  
start_time = datetime.datetime.now()  
end_time = start_time
pre_questionFromNet=None

# 初始化音频优先级管理器
from audio_priority_manager import get_audio_manager
from audio_player import stop_all_audio

# 设置音频停止回调
audio_manager = get_audio_manager()
audio_manager.set_callbacks(
    stop_music_callback=lambda: stop_all_audio(),
    stop_tts_callback=lambda: None  # TTS是阻塞的，无法中途停止
)

# 初始化ALSA设备
print("🔧 正在初始化ALSA设备...")
try:
    # 获取并初始化ALSA语音识别器
    alsa_recognizer = get_alsa_recognizer()
    if alsa_recognizer and alsa_recognizer.initialize():
        print("✅ ALSA语音识别器初始化成功")
    else:
        print("❌ ALSA语音识别器初始化失败")
    
    # 获取并初始化ALSA TTS
    alsa_tts = get_alsa_tts()
    if alsa_tts and alsa_tts.initialize():
        print("✅ ALSA CosyVoice TTS初始化成功")
    else:
        print("❌ ALSA CosyVoice TTS初始化失败")
        
    print("🎵 ALSA音频系统已准备就绪")
except Exception as e:
    print(f"⚠️ ALSA设备初始化异常: {e}")
    print("💡 程序将尝试继续运行，可能会回退到默认音频设备")

# 初始化GIF API服务（简化版）
if GIF_INTEGRATION_AVAILABLE:
    # 使用线程池启动简化的GIF API服务
    def simple_gif_service():
        """简化的GIF API服务"""
        try:
            print("🎬 正在启动GIF API服务...")
            if start_gif_service():
                print("✅ GIF API服务启动成功")
                time.sleep(1)
                gif_set_state("idle")
                # 保持服务运行
                while not thread_manager.shutdown_event.is_set() and GIF_INTEGRATION_AVAILABLE:
                    time.sleep(10)
                print("🛑 GIF API服务正常结束")
                return True
            else:
                print("❌ GIF API服务启动失败")
                return False
        except Exception as e:
            print(f"❌ GIF API服务异常: {e}")
            return False
    
    thread_manager.start_persistent_thread(
        "GIF API服务",
        ThreadType.SYSTEM_MONITOR,
        simple_gif_service,
        auto_restart=True
    )
    time.sleep(2)  # 给GIF服务一些时间启动

# 启动后台持续唤醒词监听
start_background_keyword_listening()

print("🏁 进入主监听循环 - 专注于唤醒词检测")
print("💡 新工作模式：一次唤醒 → 一次对话 → 自动待机")
print("这样避免对话线程干扰音乐播放")

print("📊 语音助手已启动，正在监听唤醒词...")
print("💡 说'你好广和通'来唤醒助手")

# 主循环 - 专注于唤醒词监听
while True and (not isrestart()):
    # 使用线程池管理提醒检查线程
    stop_do_reminder_loop=False
    reminder_future = None
    if getCheckMessage():
        reminder_future = thread_manager.submit_task(
            "提醒检查", 
            ThreadType.BACKGROUND_LISTENER, 
            do_reminder_loop
        )
        
    questionFromNet = getQuestionFromNet() if getCheckMessage() else "None"
    if pre_questionFromNet==questionFromNet:
        questionFromNet = "None" 
    else:
        pre_questionFromNet=questionFromNet 
        
    if questionFromNet == "None":
        # 播放启动欢迎音频文件（只在首次启动时播放）
        if is_first_startup:
            if os.path.exists("wakeup_word.mp3"):
                print("🎵 播放启动欢迎音频: wakeup_word.mp3")
                play_mp3_audio("wakeup_word.mp3", "system")
                print("✅ 启动欢迎音频播放完成")
            else:
                print("⚠️ wakeup_word.mp3文件不存在，使用语音合成欢迎语")
                text_to_speech(os.environ["welcome_"+lang], is_welcome=True)
            is_first_startup = False  # 标记已经播放过启动音频
        
        # 检查持续检测器事件或等待
        if wake_event_flag.is_set():
            print("🎯 检测到新的唤醒事件")
            wake_event_flag.clear()
            done = True  # 唤醒事件已在continuous_wake_callback中处理
        else:
            # 简化的主循环等待
            time.sleep(3)
            continue

    # 停止提醒循环
    stop_do_reminder_loop=True
    if reminder_future and not reminder_future.done():
        print("🛑 停止提醒检查线程...")
        reminder_future.cancel()
        time.sleep(0.5)  # 等待线程停止
    
    print("🎤 唤醒事件处理完成")
    
    # 处理网络留言信息（如果有的话）
    if questionFromNet != "None":
        print("📧 播放网络留言信息")
        text_to_speech("你好！我收到了一个留言信息，内容如下：")
        text_to_speech(questionFromNet)
    else:
        print("🎧 跳过问候语，wake.mp3已经起到提示作用，直接进入监听模式")
    
    print("✅ 唤醒处理完成，对话线程已启动")
    print("🔄 主线程继续监听唤醒词...")
    
    # 简化的对话等待逻辑
    max_wait_time = 60  # 等待60秒
    wait_start = time.time()
    
    while (time.time() - wait_start) < max_wait_time:
        # 简单检查：如果没有TTS播放，认为对话已结束
        if not tts_interrupt_flag:
            time.sleep(0.5)
        else:
            # TTS中断标志被设置，说明对话可能已结束
            time.sleep(2)  # 等待2秒确保完全结束
            break
    
    # 重置状态
    unknownCount = 0
    in_conversation_mode = False
    tts_interrupt_flag = False  # 重置中断标志
    
    print("✅ 单次对话完成，系统回到待机状态")
    
if isrestart():
    start()

# 程序结束时清理所有资源
print("🛑 正在关闭语音助手...")

# 停止后台监听
stop_background_keyword_listening()

# 关闭线程池管理器
print("🛑 关闭线程池管理器...")
shutdown_thread_manager()

print("✅ 语音助手已安全关闭") 