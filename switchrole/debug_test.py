#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试测试：检查功能注册状态
"""

def debug_function_registration():
    """调试功能注册状态"""
    print("🔍 调试功能注册状态")
    print("=" * 40)
    
    try:
        # 1. 检查功能管理器
        from function_manager import get_function_manager
        manager = get_function_manager()
        
        print("📋 功能管理器状态:")
        print(f"   已加载功能: {list(manager.functions.keys())}")
        print(f"   已注册处理器: {list(manager.function_handlers.keys())}")
        
        # 2. 检查功能处理器
        from function_handlers import get_function_handlers
        handlers = get_function_handlers()
        
        print("\n📋 功能处理器状态:")
        print(f"   当前功能: {handlers.current_function}")
        print(f"   回调函数: {list(handlers.function_callbacks.keys())}")
        
        # 3. 测试单个功能
        print("\n🧪 测试单个功能:")
        
        # 测试解析
        function_info = manager.parse_voice_command("打开作业批改")
        if function_info:
            print(f"✅ 解析成功: {function_info.name} (ID: {function_info.id})")
            
            # 测试执行
            success = manager.execute_function(function_info.id)
            print(f"{'✅' if success else '❌'} 执行结果: {success}")
        else:
            print("❌ 解析失败")
        
        # 4. 直接测试处理器方法
        print("\n🔧 直接测试处理器方法:")
        try:
            result = handlers.open_homework_qa()
            print(f"✅ 直接调用成功: {result}")
        except Exception as e:
            print(f"❌ 直接调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def test_voice_command_step_by_step():
    """逐步测试语音命令处理"""
    print("\n🎤 逐步测试语音命令处理")
    print("=" * 40)
    
    try:
        command = "打开作业批改"
        print(f"测试命令: '{command}'")
        
        # 步骤1: 获取管理器
        from function_manager import get_function_manager
        manager = get_function_manager()
        print("✅ 步骤1: 获取功能管理器成功")
        
        # 步骤2: 解析命令
        function_info = manager.parse_voice_command(command)
        if function_info:
            print(f"✅ 步骤2: 解析命令成功 -> {function_info.name} (ID: {function_info.id})")
        else:
            print("❌ 步骤2: 解析命令失败")
            return False
        
        # 步骤3: 检查处理器
        if function_info.id in manager.function_handlers:
            handler = manager.function_handlers[function_info.id]
            print(f"✅ 步骤3: 找到处理器 -> {handler}")
        else:
            print(f"❌ 步骤3: 未找到处理器，已注册的处理器: {list(manager.function_handlers.keys())}")
            return False
        
        # 步骤4: 执行功能
        success = manager.execute_function(function_info.id)
        print(f"{'✅' if success else '❌'} 步骤4: 执行功能 -> {success}")
        
        # 步骤5: 测试完整流程
        from function_handlers import handle_voice_function_command
        success, response = handle_voice_function_command(command)
        print(f"{'✅' if success else '❌'} 步骤5: 完整流程 -> {response}")
        
        return success
        
    except Exception as e:
        print(f"❌ 逐步测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 语音功能调试测试")
    print("=" * 60)
    
    # 运行调试
    debug_function_registration()
    test_voice_command_step_by_step()
    
    print("\n" + "=" * 60)
    print("🔍 调试测试完成")

if __name__ == "__main__":
    main()
