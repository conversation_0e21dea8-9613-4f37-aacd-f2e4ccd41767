#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试：验证语音功能修复效果
"""

def test_voice_commands():
    """测试语音命令"""
    print("🎤 测试语音命令处理")
    print("=" * 40)
    
    try:
        from function_handlers import handle_voice_function_command
        
        # 重点测试的命令
        test_commands = [
            "打开作业批改",
            "拍照搜题", 
            "播放音乐",
            "智能对话",
            "系统设置"
        ]
        
        success_count = 0
        
        for command in test_commands:
            print(f"\n🎤 测试命令: '{command}'")
            try:
                success, response = handle_voice_function_command(command)
                if success:
                    print(f"✅ 成功: {response}")
                    success_count += 1
                else:
                    print(f"❌ 失败: {response}")
            except Exception as e:
                print(f"❌ 异常: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_commands)} 成功")
        
        if success_count == len(test_commands):
            print("🎉 所有语音命令测试通过！")
            return True
        else:
            print("⚠️ 部分语音命令测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 语音命令测试异常: {e}")
        return False

def test_homework_correction():
    """专门测试作业批改功能"""
    print("\n📝 专门测试作业批改功能")
    print("=" * 40)
    
    try:
        from function_handlers import handle_voice_function_command
        
        homework_commands = [
            "打开作业批改",
            "作业批改功能",
            "拍照搜题",
            "批改作业"
        ]
        
        success_count = 0
        
        for command in homework_commands:
            print(f"\n🎤 测试: '{command}'")
            try:
                success, response = handle_voice_function_command(command)
                if success and "作业批改功能已启动" in response:
                    print(f"✅ 成功: {response}")
                    success_count += 1
                else:
                    print(f"❌ 失败: {response}")
            except Exception as e:
                print(f"❌ 异常: {e}")
        
        print(f"\n📊 作业批改测试结果: {success_count}/{len(homework_commands)} 成功")
        
        if success_count == len(homework_commands):
            print("🎉 作业批改功能完全正常！")
            return True
        else:
            print("⚠️ 作业批改功能部分异常")
            return False
            
    except Exception as e:
        print(f"❌ 作业批改功能测试异常: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 语音功能使用指南")
    print("=" * 40)
    
    print("🎯 现在你可以使用以下语音命令:")
    
    commands = {
        "作业批改": [
            "打开作业批改",
            "作业批改功能", 
            "拍照搜题",
            "批改作业"
        ],
        "音乐播放": [
            "播放音乐",
            "音乐播放",
            "打开音乐播放器"
        ],
        "AI对话": [
            "智能对话",
            "AI对话",
            "开始对话"
        ],
        "系统设置": [
            "系统设置",
            "打开设置",
            "设置功能"
        ]
    }
    
    for category, cmds in commands.items():
        print(f"\n📋 {category}:")
        for cmd in cmds:
            print(f"   • {cmd}")
    
    print("\n💡 使用方法:")
    print("   1. 启动语音助手")
    print("   2. 说出上述任意命令")
    print("   3. 系统会自动切换到对应功能")
    print("   4. 根据当前模式显示相应界面")

def main():
    """主函数"""
    print("🚀 语音功能修复 - 最终测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("语音命令处理", test_voice_commands),
        ("作业批改功能", test_homework_correction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 最终测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 语音功能修复完全成功！")
        show_usage_guide()
        
        print("\n🔧 修复内容总结:")
        print("   ✅ 修复了 function_manager 模块导入问题")
        print("   ✅ 修复了语音命令解析逻辑")
        print("   ✅ 修复了功能处理器注册问题")
        print("   ✅ 确保作业批改功能正常工作")
        print("   ✅ 优化了错误处理和返回逻辑")
        
        print("\n🎯 现在语音助手可以正确识别并执行功能切换命令了！")
        return True
    else:
        print("⚠️ 部分功能仍有问题，需要进一步检查")
        return False

if __name__ == "__main__":
    main()
