# 作业问答系统完整实现

## 概述

本系统实现了一个完整的作业问答功能，包含三个主要步骤：
1. **拍摄题目** - 用户拍摄需要问答的题目照片
2. **语音输入** - 用户语音描述科目和遇到的难点
3. **数据上传** - 系统将照片和语音识别结果上传到服务器进行AI分析

## 功能特性

### 1. 完整的流程控制
- **TODO列表界面** - 直观显示三个步骤的完成状态
- **状态管理** - 精确控制每个步骤的执行顺序
- **MQTT控制** - 支持远程指令控制流程

### 2. 拍照功能
- **复用拍照搜题摄像头** - 利用现有的摄像头基础设施
- **实时预览** - 摄像头画面实时预览
- **照片转换** - 自动将照片转换为base64格式

### 3. 语音识别
- **阿里云集成** - 使用DashScope SDK进行语音识别
- **智能解析** - 自动从语音中提取科目和难点信息
- **音频提示** - 播放zhishi.wav提示用户说话
- **多种科目支持** - 支持数学、物理、化学、英语等多种学科

### 4. 数据上传
- **JSON格式** - 按照指定格式组织数据
- **服务器通信** - 上传到http://poem.e5.luyouxia.net:21387/
- **错误处理** - 完整的错误处理和重试机制

## 文件结构

```
homework_qa_handler.py      # 作业问答核心处理器
homework_qa_page.py         # 作业问答界面页面
voice_recognition.py        # 阿里云语音识别模块
test_homework_qa_system.py  # 完整系统测试脚本
```

## 核心组件

### HomeworkQAHandler
作业问答的核心处理器，负责：
- 流程状态管理
- MQTT指令处理
- 摄像头控制
- 语音识别协调
- 数据上传

### HomeworkQAPage
用户界面组件，包含：
- **TodoStepWidget** - 单个步骤显示组件
- **TODO列表面板** - 显示三个步骤的完成状态
- **摄像头预览面板** - 实时显示摄像头画面
- **状态提示** - 当前流程状态显示

### AliCloudVoiceRecognizer
语音识别模块，实现：
- 音频录制功能
- DashScope SDK集成
- 语音文本解析
- 科目难点提取

## 使用说明

### 1. 环境配置

确保已安装必要依赖：
```bash
pip install dashscope requests
```

设置阿里云API密钥：
```bash
export DASHSCOPE_API_KEY="your-api-key-here"
```

### 2. 启动系统

```python
from homework_qa_page import HomeworkQAPage

# 创建作业问答页面
qa_page = HomeworkQAPage()
qa_page.show()
```

### 3. MQTT控制指令

- `6-0-1` (confirm) - 确认/拍照/开始语音识别
- `6-0-2` (back) - 返回主菜单
- `6-0-3` (next) - 下一步/跳过语音识别/开始上传

### 4. 完整流程

1. **启动** - 点击"开始流程"或发送`6-0-1`指令
2. **拍照** - 系统等待拍照指令，发送`6-0-1`执行拍照
3. **语音输入** - 拍照完成后进入语音识别阶段
   - 发送`6-0-1`开始语音识别
   - 发送`6-0-3`跳过语音识别
4. **数据上传** - 发送`6-0-3`开始上传数据到服务器
5. **完成** - 显示上传结果，流程结束

## 数据格式

### 上传到服务器的JSON格式：
```json
{
    "student_name": "默认用户",
    "gender": "未知", 
    "des": "数学",                    // 科目
    "details": "函数求导问题",         // 难点
    "picture": "base64_encoded_image"  // 照片的base64编码
}
```

### 语音识别结果格式：
```json
{
    "subject": "数学",                // 识别的科目
    "difficulty": "函数求导问题",     // 识别的难点
    "original_text": "我在数学方面遇到了函数求导问题"  // 原始识别文本
}
```

## 状态管理

### 步骤状态
- **pending** - 待执行（灰色圆圈）
- **current** - 当前步骤（蓝色数字）
- **completed** - 已完成（绿色对号）

### 流程状态
- `current_step`: 当前执行步骤（1=拍照, 2=语音, 3=上传）
- `is_processing`: 是否正在处理中
- `waiting_for_mqtt`: 是否等待MQTT指令

## 错误处理

### 摄像头错误
- 自动重试摄像头初始化
- 显示具体错误信息
- 支持摄像头重启

### 语音识别错误
- API密钥检查
- 自动降级到模拟模式
- 音频录制失败处理

### 网络上传错误
- 连接超时处理
- 服务器错误提示
- 重试机制

## 测试

运行完整系统测试：
```bash
python test_homework_qa_system.py
```

测试功能包括：
- 界面显示测试
- 流程控制测试
- MQTT指令测试
- 状态更新测试

## 注意事项

1. **音频文件** - 确保`zhishi.wav`文件存在于项目根目录
2. **摄像头权限** - 确保应用有摄像头访问权限
3. **网络连接** - 语音识别和数据上传需要网络连接
4. **API配额** - 注意阿里云API的使用配额限制
5. **临时文件** - 系统会自动清理录音临时文件

## 扩展功能

### 科目识别优化
可以在`voice_recognition.py`中添加更多科目关键词：
```python
subjects_map = {
    '数学': ['数学', '算术', '代数', '几何', '微积分', '函数', '方程'],
    # 添加更多科目...
}
```

### 服务器响应处理
可以扩展上传完成后的响应处理，显示AI分析结果。

### 多语言支持
可以扩展语音识别支持多种语言的科目描述。

## 维护建议

1. 定期检查DashScope SDK更新
2. 监控API使用量和成本
3. 备份重要的配置文件
4. 测试不同网络环境下的稳定性
5. 收集用户反馈优化识别准确性 